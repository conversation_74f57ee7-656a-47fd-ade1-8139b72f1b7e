# TECHNICAL_SPEC.md

# Lineup Lab — Technical Specification

This document specifies the recommended technology choices and the concrete data models for the proof-of-concept (POC), the minimum viable product (MVP) stage, and a long-term production stage. It also contains concrete recommended TypeScript interfaces and a hard-coded POC dataset to drive the UI scaffolding.

---

## 1. Recommended technology stack (by stage)

### A. Proof of Concept (current stage — fastest development)
- **Frontend framework**: **Vue 3** + **TypeScript** (Vite dev server).  
- **Component library**: **Vuetify 3** (Material design; excellent out-of-the-box visuals with minimal styling effort).  
- **Drag & Drop**: **vue-draggable-next** (SortableJS wrapper) for list ↔ grid box drag/drop; custom handlers for Ctrl+drag copy semantics and more granular drop handling.  
- **Local persistence**: **Dexie.js (IndexedDB)** for local-first storage during POC. Provide export/import JSON and CSV downloads for robust backups.  
- **Build / packaging (later)**: Vite build; placeholder to integrate with **Tauri** for desktop packaging when desired.  
- **Other utilities**: `uuid` for ids, `date-fns` or native Date for game timestamps, a small keyboard shortcut handler (or global keydown listeners), and a minimal table/csv helper for CSV exports.

**Why**: Vue 3 + TypeScript + Vuetify + Vite gives the fastest dev experience while looking great; Dexie is simple to use and saves time over running a local backend.

---

### B. Minimum Viable Product (short future stage)
- **Frontend**: Vue 3 + TypeScript + Vuetify (same), increased client-side structure.  
- **Persistence** (option 1, single-machine local): migrate to a file-backed SQLite using a desktop wrapper (Tauri) — SQLite file stored in user documents.  
- **Persistence** (option 2, local server): simple local Node/Express backend with SQLite file (`better-sqlite3`) exposing a small REST API (no cloud). Useful if you want multi-window or multi-app integrations.  
- **Search & indexing**: use small in-app search (fuzzy matching) for player name autocomplete; keep small server endpoints only if required by scale.  
- **Testing**: some unit tests for core utilities (rule engine, import/export) — but still lightweight.

---

### C. Production (far future, if you publish)
- **Frontend**: Vue 3 + TypeScript (Vite), optionally port to SPAs with server-side rendering if SEO needed.  
- **Backend**: PostgreSQL (or managed DB) + REST or GraphQL API behind an authenticated layer. Containerize (Docker), auto-deploy via CI/CD.  
- **Auth**: OAuth / Auth0 / self-hosted JWT (depending on requirements).  
- **Storage**: stable SQL DB; photos stored in object storage (S3) with strong backups.  
- **Scaling**: horizontal API + stateless frontend; analytics and export endpoints.  
- **Monitoring & backups**: regular DB snapshots, file/object storage replication, and automated backups.

### Suggested dependencies (POC)

```json
"dependencies": {
  "vue": "^3.x",
  "vue-router": "^4.x",
  "vuetify": "^3.x",
  "vue-draggable-next": "^2.x",
  "dexie": "^3.x",
  "uuid": "^9.x",
  "fuse.js": "^6.x"         // optional: fuzzy name search
}
"devDependencies": {
  "vite": "^5.x",
  "typescript": "^5.x"
}
```

---

## 2. Storage & reliability notes (POC choice & path to stronger guarantees)

**POC (Dexie / IndexedDB)**  
- Pros: no backend, quick to implement, survives browser restarts, queryable locally.  
- Cons: browser profile deletion or clearing can remove data. Mitigation: automatic export endpoint (periodic "Download backup" button) and explicit JSON & CSV export UI.

**More reliable options (future)**  
- **Tauri + SQLite** (recommended path for desktop local reliability): file-backed SQLite stored in a user documents folder — robust and portable, not prone to browser data-clearing. Tauri also gives a path to integrate native file pickers for importing photos.  
- **Local Node + SQLite**: if you prefer a local server approach; robust but requires keeping a local process running.

**Recommendation for now**: Implement Dexie for speed with obligatory "Export all data (JSON)" and "Export lineup (CSV)" flows; treat the JSON export as the canonical manual backup. Add a nightly or user-triggered automatic backup that downloads the JSON file to the local filesystem.

---

## 3. Data model — TypeScript interfaces (POC)

Use these interfaces in the POC; they are deliberately small and explicit so you can hardcode the POC dataset.

```ts
// unique id type
type ID = string;

interface Player {
  id: ID;                    // global player id (not tied to team)
  firstName: string;
  lastName: string;
  throws?: 'L' | 'R' | 'S';
  bats?: 'L' | 'R' | 'S';    // S = switch
  dob?: string | null;       // optional
}

interface Team {
  id: ID;
  name: string;              // "Dallastown Cougars"
  season: string;            // "Fall 2025"
  headCoach?: string;
  assistantCoaches?: string[];
  notes?: string;
  games?: Game[];            // associated games for this team
}

interface TeamPlayer {
  id: ID;                    // relationship id
  teamId: ID;
  playerId: ID;
  jerseyNumber?: string | number;
  preferredPositions: string[]; // e.g. ['P','1B','3B','LF']
  facePhotoId?: ID;          // optional reference to Photo table (photo is team-scoped)
}

interface Photo {
  id: ID;
  teamId: ID;                // photo belongs to a team snapshot
  playerId?: ID | null;      // optional, team-player specific
  filename?: string;         // original filename or label
  mimeType?: string;
  blob?: Blob | string;      // stored blob or base64 (Dexie supports blobs)
  createdAt: string;
}

interface Game {
  id: ID;
  teamId: ID;
  date: string;              // required game date (ISO format)
  gameTime?: string;         // scheduled game time (e.g., "6:00 PM")
  arrivalTime?: string;      // team arrival time (e.g., "5:30 PM")
  opponent: string;          // required opponent team name
  homeAway: 'Home' | 'Away'; // required home/away designation
  address?: string;          // game location address
  notes?: string;            // coach notes, special instructions
  inningCount: number;       // number of innings for this game (customizable, typically 12-16)
}

interface LineupCell {
  id: ID;                    // e.g., `${gameId}:i${inning}:pos${positionKey}`
  gameId: ID;
  inning: number;            // 1..N (N customizable per game)
  positionKey: string;       // canonical position id, e.g., 'P','C','1B','Bench1'
  playerId?: ID | null;      // assigned player or null
  createdAt: string;
  updatedAt?: string;
}
```

---

## 4. Sample Data

```json
{
  "teams": [
    {
      "id":"team-1",
      "name":"Dallastown Cougars 12UB Robertson",
      "season":"Fall 2025",
      "headCoach":"Coach Robertson",
      "assistantCoaches":["Assistant 1"]
    }
  ],
  "players": [
    {"id":"p-1","firstName":"Adrian","lastName":""},
    {"id":"p-2","firstName":"Ben","lastName":""},
    {"id":"p-3","firstName":"Bennett"},
    {"id":"p-4","firstName":"Caleb"},
    {"id":"p-5","firstName":"Cam"},
    {"id":"p-6","firstName":"Christian"},
    {"id":"p-7","firstName":"Jack"},
    {"id":"p-8","firstName":"Jaxon"},
    {"id":"p-9","firstName":"Kaleb"},
    {"id":"p-10","firstName":"Luca"},
    {"id":"p-11","firstName":"Mason"},
    {"id":"p-12","firstName":"Reid"},
    {"id":"p-13","firstName":"Ryan"},
    {"id":"p-14","firstName":"Seth"},
    {"id":"p-15","firstName":"Tyler"}
  ],
  "teamPlayers": [
    {"id":"tp-1","teamId":"team-1","playerId":"p-1","jerseyNumber":"1","preferredPositions":["C","1B","3B"]},
    {"id":"tp-2","teamId":"team-1","playerId":"p-2","jerseyNumber":"2","preferredPositions":["LF","RF"]},
    // ... others ...
    {"id":"tp-15","teamId":"team-1","playerId":"p-15","jerseyNumber":"15","preferredPositions":["SS","2B"]}
  ],
  "games": [
    {"id":"g-1","teamId":"team-1","date":"2025-09-12","inningCount":12}
  ],
  "lineupCells": [
    // empty cells initially; UI will render a grid for innings 1..12 and positions columns.
  ]
}
```

---

## 5. UI and UX Architecture

### Main screen sections

Top bar: application title, theme toggle, export/import buttons.

Left drawer: Three-state navigation system:
- **Teams view**: List of all teams with player/game counts and season info
- **Games view**: List of games for selected team with opponent, date, time, and home/away status
- **Lineup view**: Player roster for selected game with draggable items showing name, jersey, throw/bat icons, preferred positions, thumbnail photo (if present)

Main area: Three-state content system:
- **Teams view**: Team selection interface with guidance
- **Games view**: Game cards showing detailed game information (opponent, date, times, location, notes)
- **Lineup view**: Dynamic grid (rows = innings based on game.inningCount, columns = canonical positions). Each cell is droppable and keyboard-focusable.

Right pane (future): rule toggles + style configurator + quick stats summary for the current lineup.

### Drag & drop

vue-draggable-next (SortableJS) to allow dragging from roster (left drawer) into the grid and between grid cells.

Implement onEnd handler that inspects the native event for ctrlKey to decide copy vs move. When copying, do not clear the source cell. Also implement Clone behavior when dragging from roster to grid (roster should be clonable).

### Keyboard behaviors

Global key listeners for shortcuts (scoped when the grid is focused):

Arrow keys: move the current cell focus (left, right, up, down).

Enter: open an inline editable autocomplete combo box populated with roster players (typing filters). Selecting a name fills the cell.

Delete/Backspace: clears focused cell(s).

Ctrl+C: copy focused cell (only allowed when one cell is focused).

Ctrl+X: cut focused cell (only allowed when one cell focused).

Ctrl+V: paste into focused cell or multi-cell selection (paste repeated into each cell in selection).

Ctrl+Click: toggle multi-select of a grid cell. When multiple cells selected, disable Cut/Copy; Enable Paste to fill selection.

Visual: focused cell gets a distinct border; selected player appearances highlight with a consistent accent color across the grid and roster.

### Inline editing & autocomplete

Use Vuetify's Autocomplete/Combobox component inside the cell editor. Implement fuzzy filtering by firstName + lastName. Support keyboard completion and mouse selection.

### Rule engine (simple & UI-driven)

Represent each rule as a small JS function that takes (game, lineupGrid, params) and returns flagged cell ids. Example rule configs:

bench-back-to-back: detect players assigned to bench in consecutive innings and flag. Params: maxBackToBack (e.g., 1).

not-infield-X: for each player, if they have no infield assignments in a sliding window of X innings, flag cells.

not-preferred-position: flags cells where assigned position not in TeamPlayer.preferredPositions.

The UI allows toggling each rule and picking a style (background color, border, icon). The rules are reactive — re-calc on each grid change.

### Photos

Photos are team-scoped. Photo table stores a blob (IndexedDB blob) or a file path (when migrating to Tauri/SQLite). UI for photo upload associates the blob with a TeamPlayer.facePhotoId. Copying a player from Team A to Team B can optionally clone the photo.

### Export formats

CSV: represent the grid rows as innings. CSV header: Inning, P, C, 1B, 2B, SS, 3B, LF, CF, RF, Bench1, Bench2.... Copy CSV to clipboard (format suitable for Excel) and offer to download .csv.

JSON: full export of selected team + games + lineups for backup or import.

---

## 6. POC Acceptance Criteria

- The app boots in localhost and shows three teams with different player counts (9, 12, 15) and game schedules.
- Three-level navigation: Teams → Games → Lineup creation works smoothly with proper back navigation.
- Game management displays comprehensive game details (opponent, date, times, location, home/away, notes).
- Dynamic lineup grid adjusts inning count based on selected game (12-16 innings).
- Left drawer shows appropriate content based on navigation state (teams/games/players).
- Future: Drag a player into any cell (and between cells). Ctrl+drag duplicates.
- Future: Clicking a player highlights all occurrences of that player.
- Future: Arrow keys change focused cell; Enter opens a combobox with autocomplete; Delete clears the cell.
- Future: Rule toggles visually highlight cells per the enabled rule.
- Future: You can export the current lineup as CSV (clipboard and saved file) and export JSON backups.

---

## 7. Future Migration Paths

- To Tauri + SQLite: map the interfaces above to SQL tables. Photos can be BLOBs in SQLite or stored as files with paths in SQLite. Tauri provides a safe path to file-backed persistence and native file access for robust backups.

- To cloud: create a small REST API with endpoints for teams, players, games, lineups. Use PostgreSQL and S3 for photos. Add OAuth if publishing.