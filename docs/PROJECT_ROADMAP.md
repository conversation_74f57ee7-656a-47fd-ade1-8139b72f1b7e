
---

```markdown
# ROADMAP.md

# Lineup Lab — Roadmap (Proof-of-Concept → MVP → Next)

This roadmap breaks development into focused, minimal phases so you can build a reliable proof-of-concept quickly, then add features in small, testable batches.

---

## Phase 0 — Project skeleton & team/game navigation (POC start) ✅ COMPLETE
**Goal:** get a working local UI with team and game management navigation flow.
**Deliverables**
- ✅ Vite + Vue 3 + TypeScript + Vuetify scaffold.
- ✅ Three-level navigation: Teams → Games → Lineup creation.
- ✅ Left drawer shows teams list, games list, or player roster based on navigation state.
- ✅ Mock data for 3 teams with varying numbers of players (9, 12, 15) and games (2-4 games each).
- ✅ Game management with full details: opponent, home/away, date, times, address, notes.
- ✅ Game view grid: renders dynamic innings (12-16 based on game) × canonical position columns (P,C,1B,2B,SS,3B,LF,CF,RF,DH).
- ✅ Responsive design with proper back navigation between levels.
**Acceptance criteria**
- ✅ App starts, shows team selection, navigates through games, displays game details, and shows lineup grid with correct inning count per game.

---

## Phase 1 — Core interactions & keyboard basics
**Goal:** make drag/drop and keyboard editing flow smoothly.
**Enhanced Deliverables**
- **Grid System Architecture:**
  - Replace v-text-field components with custom LineupCell components that support all interaction modes
  - Implement grid state management with reactive data structure for position assignments
  - Add cell focus management system with visual feedback for active/selected cells

- **Drag & Drop Implementation (using vue-draggable-next):**
  - Roster → Grid: Clone player by default (preserves roster), visual drag preview with player names
  - Grid → Grid: Move player by default, Ctrl+drag to duplicate
  - Roster → Roster: Reorder functionality for preferred lineup order
  - Visual drop zones with highlight effects and position validation
  - Drag constraints: prevent invalid drops (e.g., can't drop on occupied cells without confirmation)

- **Enhanced Player Selection & Highlighting:**
  - Click highlights ALL occurrences of selected player across entire grid with distinct styling
  - Current focused cell gets emphasized border (different from selection highlight)
  - Player count indicator shows how many innings each player is assigned
  - Visual distinction between different player states (active, benched, overused)

- **Comprehensive Keyboard Navigation:**
  - Arrow keys navigate between cells (Up/Down: positions, Left/Right: innings)
  - Tab/Shift+Tab for logical navigation flow
  - Enter opens inline autocomplete with filtered player suggestions
  - Escape cancels editing and returns to navigation mode
  - Delete/Backspace clears focused cell
  - Home/End jumps to first/last inning in current position row

- **Advanced Clipboard Operations:**
  - Ctrl+C copies player from focused cell
  - Ctrl+X cuts player (clears cell and copies to clipboard)
  - Ctrl+V pastes player to focused cell or fills multi-selection
  - Multi-select with Ctrl+Click (toggles individual cells)
  - Disable Cut/Copy operations when multiple cells selected (prevent ambiguity)
  - Visual feedback for clipboard operations with toast notifications

- **User Experience Enhancements:**
  - Undo/Redo functionality (Ctrl+Z/Ctrl+Y) with action history
  - Auto-save indication and conflict resolution for simultaneous edits
  - Loading states and error handling for all interactions
  - Accessible design with proper ARIA labels and keyboard hints
  - Touch gesture support for mobile/tablet usage

- **Performance & Responsiveness:**
  - Efficient rendering for large grids (16+ innings)
  - Smooth animations for drag operations and state changes
  - Debounced auto-save to prevent excessive state updates
  - Optimized re-rendering with Vue's reactivity system

**Enhanced Acceptance Criteria**
- All drag/drop interactions work smoothly with visual feedback and validation
- Keyboard navigation provides complete grid control without mouse dependency
- Player highlighting and selection states are visually clear and consistent
- Clipboard operations handle edge cases gracefully (empty cells, multi-selection)
- Performance remains smooth with full roster (15 players) across maximum innings (16)
- Touch interactions work correctly on tablet devices
- Undo/redo maintains consistent state across all interaction types
- Accessibility features support screen readers and keyboard-only navigation

---

## Phase 2 — Selection UX polish & multi-selection behavior
**Goal:** robust selection UX and cell manipulation.  
**Deliverables**
- Visual styles for selected/focused cells and multi-selection.  
- Ctrl+Click multi-select, Shift+Click range-select optional.  
- Paste fills multi-selected cells in row-major order.  
- Add contextual actions: right-click context menu on cell(s) with quick "Clear", "Copy player", "Select player everywhere".  
**Acceptance criteria**
- Multi-selection behaves predictably; paste and clear act on selections as specified.

---

## Phase 3 — Rule engine & highlight styling
**Goal:** implement the rule-based highlighting engine and rule-edit UI.  
**Deliverables**
- Simple rule registry (toggleable rules) with parameters and style settings per rule.  
- Implement core rules:
  - Bench back-to-back detection
  - Not-infield over X innings
  - Not-outfield over X innings
  - Not in preferred position
- UI to toggle rules and choose per-rule style (background color, border).  
**Acceptance criteria**
- Rules can be toggled on/off and applied to the current game grid; flagged cells are styled per rule settings.

---

## Phase 4 — CSV / JSON export + clipboard support
**Goal:** reliable export for backup and Excel integration.  
**Deliverables**
- Export current grid as CSV and copy CSV to clipboard (suitable for Excel).  
- Download CSV file.  
- Full backup: export entire team/game/lineup as JSON.  
- Import JSON (basic validation and merge/replace options).  
**Acceptance criteria**
- You can export a lineup to clipboard and download JSON; you can import JSON to restore state.

---

## Phase 5 — Local persistence (Dexie) + graceful backups
**Goal:** persist teams/games/lineups locally and implement backup strategy.  
**Deliverables**
- Dexie DB schema for Teams, Players, TeamPlayers, Games, LineupCells, Photos.  
- Auto-save of edits.  
- Backup buttons: Manual Export (JSON), Auto-Export button that triggers a download of the JSON snapshot.  
- Import UI with basic conflict handling (replace vs merge).  
**Acceptance criteria**
- Changes survive page reload; user can restore from a previously exported JSON file.

---

## Phase 6 — Multi-game stats & simple reports
**Goal:** compute per-player position statistics over last X games / season.  
**Deliverables**
- DB queries and UI to show: times infield, outfield, benched for last X games and season to date.  
- Small charts or simple frequency tables in the right pane.  
**Acceptance criteria**
- For the current team, you can request stats for last 5 games and see counts/percentages by position category.

---

## Phase 7 — Photos & team snapshots
**Goal:** team-scoped photos and season snapshot behavior.  
**Deliverables**
- Photo upload UI that associates an image blob with a TeamPlayer.photoId.  
- When copying a player to a new team, prompt to copy photo or upload new one.  
- When browsing old seasons, photos match the historical team snapshot.  
**Acceptance criteria**
- Team photos are preserved per-team; copying players between teams optionally copies photos.

---

## Phase 8 — Prepare for migration to Tauri + SQLite (optional)
**Goal:** create migration path for robust file-backed local persistence and optional desktop packaging.  
**Deliverables**
- Mapping of current Dexie scheme to normalized SQLite tables.  
- Small CLI or script to export JSON → SQLite (for data migration).  
- Placeholder Tauri config files.  
**Acceptance criteria**
- A JSON export from the POC can be ingested into a SQLite schema ready for Tauri packaging.

---

## Phase 9 — Polish, accessibility, and optional features
**Possible future items**
- Inning/field view (diamond UI per inning).  
- Auto-lineup optimizer.  
- Cloud sync and sharing (if you decide to publish).  
- Mobile/touch improvements and tablet layout.  
**Acceptance criteria**
- Features tested, accessible, and documented.

---

## Notes on iteration strategy
- Start small and test each phase with the single hardcoded team of 15 players. Only after you can produce lineups reliably should you add persistence. Rigorously test import/export before trusting Dexie as sole storage.

---

