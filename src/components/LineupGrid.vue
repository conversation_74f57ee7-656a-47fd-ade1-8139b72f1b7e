<template>
  <div
    ref="gridRef"
    class="lineup-grid-container"
    @keydown="handleGlobalKeydown"
    @click="handleGridClick"
    role="grid"
    :aria-label="`Lineup grid for ${game.opponent} game with ${game.inningCount} innings`"
    aria-describedby="grid-instructions"
    tabindex="-1"
  >
    <!-- Screen reader instructions -->
    <div id="grid-instructions" class="sr-only">
      Use arrow keys to navigate between cells. Press Enter to edit a cell. Press Delete to clear a cell. Use Ctrl+C to copy, Ctrl+X to cut, and Ctrl+V to paste players. Use Ctrl+Z to undo and Ctrl+Y to redo actions.
    </div>

    <!-- Grid Header -->
    <div class="grid-header" role="rowgroup">
      <div class="position-header" role="columnheader">Position</div>
      <div
        v-for="inning in game.inningCount"
        :key="`header-${inning}`"
        class="inning-header"
        role="columnheader"
        :aria-label="`Inning ${inning}`"
      >
        Inning {{ inning }}
      </div>
    </div>

    <!-- Grid Body -->
    <div class="grid-body" role="rowgroup">
      <div
        v-for="position in POSITIONS"
        :key="`row-${position}`"
        class="position-row"
        role="row"
        :aria-label="`${position} position row`"
      >
        <div class="position-label" role="rowheader" :aria-label="`${position} position`">
          {{ position }}
        </div>
        <draggable
          :list="getRowData(position)"
          group="lineup"
          item-key="inning"
          class="position-cells"
          :data-position="position"
          @change="onDragChange"
        >
          <template #item="{ element: cell }">
          <LineupCell
            :key="`${position}-${cell.inning}`"
            :game-id="game.id"
            :inning="cell.inning"
            :position-key="position"
            :player-id="getPlayerId(cell.inning, position)"
            :players="players"
            :is-focused="isCellFocused(cell.inning, position)"
            :is-selected="isCellSelected(cell.inning, position)"
            :selected-player-id="selectedPlayerId"
            :is-editing="isEditing(cell.inning, position)"
            :is-focusable="true"
            @update:player-id="(playerId) => assignPlayer(cell.inning, position, playerId)"
            @focus="handleCellFocus"
            @edit-start="handleEditStart"
            @edit-end="handleEditEnd"
            @click="handleCellClick"
            @keydown="handleCellKeydown"
            class="grid-cell"
          />
        </template>
        </draggable>
      </div>
    </div>

    <!-- Keyboard hints -->
    <div v-if="showKeyboardHints" class="keyboard-hints">
      <v-chip size="small" variant="text">
        <v-icon start size="small">mdi-keyboard</v-icon>
        Arrows: Navigate • Enter: Edit • Del: Clear • Ctrl+C/X/V: Copy/Cut/Paste • Ctrl+Z/Y: Undo/Redo
      </v-chip>
    </div>

    <!-- Toast notifications -->
    <v-snackbar
      v-model="showToast"
      :timeout="2000"
      :color="toastColor"
      location="bottom"
    >
      {{ toastMessage }}
    </v-snackbar>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import draggable from 'vuedraggable'
import LineupCell from './LineupCell.vue'
import { useLineupGrid } from '../composables/useLineupGrid'
import type { Player, Game, FocusPosition } from '../types'
import { POSITIONS } from '../types'

interface Props {
  game: Game
  players: Player[]
  showKeyboardHints?: boolean
}

interface Emits {
  (e: 'player-select', playerId: string): void
  (e: 'cell-change', data: { inning: number, position: string, playerId: string | null }): void
  (e: 'drag-start', data: any): void
  (e: 'drag-end'): void
}

const props = withDefaults(defineProps<Props>(), {
  showKeyboardHints: true
})

const emit = defineEmits<Emits>()

const gridRef = ref<HTMLElement>()

// Toast notifications
const showToast = ref(false)
const toastMessage = ref('')
const toastColor = ref<'success' | 'error' | 'warning' | 'info'>('info')

// Initialize grid composable
const {
  focusedCell,
  editingCell,
  selectedCells,
  selectedPlayerId,
  selectionState,
  getPlayerId,
  setFocusedCell,
  clearFocus,
  startEditing,
  stopEditing,
  isEditing,
  highlightPlayerOccurrences,
  clearPlayerHighlight,
  clearSelection,
  isCellSelected,
  navigate,
  copyCell,
  cutCell,
  pasteCell,
  assignPlayer,
  clearCell,
  undo,
  redo,
  getPlayerCount
} = useLineupGrid(props.game, props.players)

// Grid data for draggable
const getRowData = (position: string) => {
  const data = []
  for (let inning = 1; inning <= props.game.inningCount; inning++) {
    const playerId = getPlayerId(inning, position)
    const player = playerId ? props.players.find(p => p.id === playerId) : null

    data.push({
      inning,
      position,
      id: `${position}-${inning}`,
      playerId: playerId,
      player: player,
      // Include player data for vuedraggable if there's a player assigned
      ...(player && {
        firstName: player.firstName,
        lastName: player.lastName,
        name: `${player.firstName} ${player.lastName || ''}`.trim()
      })
    })
  }
  return data
}

// Focus management
const isCellFocused = (inning: number, position: string): boolean => {
  if (!focusedCell.value) return false
  return focusedCell.value.inning === inning && focusedCell.value.positionKey === position
}

const handleCellFocus = (data: FocusPosition) => {
  setFocusedCell(data)

  // Focus the grid container for keyboard navigation
  if (gridRef.value) {
    gridRef.value.focus()
  }
}

const handleEditStart = () => {
  if (focusedCell.value) {
    startEditing(focusedCell.value)
  }
}

const handleEditEnd = () => {
  stopEditing()

  // Return focus to grid
  if (gridRef.value) {
    gridRef.value.focus()
  }
}

// Click handling
const handleCellClick = () => {
  if (selectionState.value === 'multi') {
    // Clear multi-selection on normal click
    clearSelection()
  }
}

const handleGridClick = (event: MouseEvent) => {
  // If clicking outside cells, clear focus and selection
  if (!event.target || !(event.target as Element).closest('.grid-cell')) {
    clearFocus()
    clearSelection()
    clearPlayerHighlight()
  }
}

// Keyboard handling
const handleGlobalKeydown = (event: KeyboardEvent) => {
  // Don't handle keys if we're editing
  if (editingCell.value) return

  const isCtrl = event.ctrlKey || event.metaKey

  // Global shortcuts
  if (isCtrl) {
    switch (event.key.toLowerCase()) {
      case 'c':
        event.preventDefault()
        copyCell()
        showToastMessage('Copied player to clipboard', 'info')
        break
      case 'x':
        event.preventDefault()
        cutCell()
        showToastMessage('Cut player to clipboard', 'info')
        break
      case 'v':
        event.preventDefault()
        pasteCell()
        showToastMessage('Pasted player from clipboard', 'success')
        break
      case 'z':
        if (event.shiftKey) {
          event.preventDefault()
          if (redo()) {
            showToastMessage('Redo successful', 'success')
          }
        } else {
          event.preventDefault()
          if (undo()) {
            showToastMessage('Undo successful', 'success')
          }
        }
        break
      case 'y':
        event.preventDefault()
        if (redo()) {
          showToastMessage('Redo successful', 'success')
        }
        break
    }
    return
  }

  // Navigation keys
  if (focusedCell.value) {
    switch (event.key) {
      case 'ArrowUp':
        event.preventDefault()
        navigate('up')
        break
      case 'ArrowDown':
        event.preventDefault()
        navigate('down')
        break
      case 'ArrowLeft':
        event.preventDefault()
        navigate('left')
        break
      case 'ArrowRight':
        event.preventDefault()
        navigate('right')
        break
      case 'Home':
        event.preventDefault()
        navigate('home')
        break
      case 'End':
        event.preventDefault()
        navigate('end')
        break
      case 'Enter':
        event.preventDefault()
        handleEditStart()
        break
      case 'Delete':
      case 'Backspace':
        event.preventDefault()
        if (selectionState.value === 'multi') {
          // Clear all selected cells
          let clearedCount = 0
          for (const key of selectedCells.value) {
            const parts = key.split(':')
            if (parts.length >= 3) {
              const inning = parseInt(parts[1].substring(1))
              const position = parts[2].substring(3)
              if (getPlayerId(inning, position)) {
                clearCell(inning, position)
                clearedCount++
              }
            }
          }
          clearSelection()
          if (clearedCount > 0) {
            showToastMessage(`Cleared ${clearedCount} cells`, 'success')
          }
        } else if (focusedCell.value) {
          const { inning, positionKey } = focusedCell.value
          if (getPlayerId(inning, positionKey)) {
            clearCell(inning, positionKey)
            showToastMessage('Cell cleared', 'success')
          }
        }
        break
      case 'Escape':
        event.preventDefault()
        clearSelection()
        clearPlayerHighlight()
        break
    }
  }
}

const handleCellKeydown = () => {
  // Cell-specific keydown handling is done in LineupCell component
  // This is for additional grid-level handling if needed
}

// Drag and drop handling
const onDragChange = (event: any) => {
  // Handle the change event from vuedraggable
  if (event.added) {
    // An item was added to this position row
    const addedItem = event.added
    const playerId = addedItem.element?.playerId || addedItem.element?.id
    const targetPosition = addedItem.element?.position
    const targetInning = addedItem.newIndex + 1 // Convert to 1-based inning

    if (playerId && targetPosition && targetInning) {
      // Sync with our lineup state
      assignPlayer(targetInning, targetPosition, playerId)
      showToastMessage('Player assigned from roster', 'success')
    }
  }

  if (event.removed) {
    // An item was removed from this position row
    const removedItem = event.removed
    const targetPosition = removedItem.element?.position
    const targetInning = removedItem.oldIndex + 1

    if (targetPosition && targetInning) {
      // Clear from our lineup state
      clearCell(targetInning, targetPosition)
      showToastMessage('Player removed', 'info')
    }
  }
}

// Toast notifications
const showToastMessage = (message: string, color: 'success' | 'error' | 'warning' | 'info' = 'info') => {
  toastMessage.value = message
  toastColor.value = color
  showToast.value = true
}

// Handle player selection from external sources
const handlePlayerSelect = (playerId: string) => {
  highlightPlayerOccurrences(playerId)
  emit('player-select', playerId)
}

// Focus on mount
onMounted(() => {
  if (gridRef.value) {
    gridRef.value.focus()
  }
})

// Cleanup
onUnmounted(() => {
  // Any cleanup if needed
})

// Expose methods for external use
defineExpose({
  focusCell: (inning: number, position: string) => {
    setFocusedCell({
      gameId: props.game.id,
      inning,
      positionKey: position
    })
  },
  clearFocus,
  getPlayerCount: computed(() => getPlayerCount.value),
  highlightPlayer: handlePlayerSelect,
  clearHighlight: clearPlayerHighlight
})
</script>

<style scoped>
.lineup-grid-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  outline: none;
  position: relative;
  overflow: auto;
}

.grid-header {
  display: flex;
  background: #f5f5f5;
  border-bottom: 2px solid #ddd;
  position: sticky;
  top: 0;
  z-index: 3;
}

.position-header {
  min-width: 80px;
  max-width: 80px;
  padding: 12px 8px;
  font-weight: 600;
  background: #e0e0e0;
  border-right: 1px solid #ddd;
  display: flex;
  align-items: center;
  justify-content: center;
}

.inning-header {
  min-width: 120px;
  max-width: 120px;
  padding: 12px 8px;
  font-weight: 500;
  text-align: center;
  border-right: 1px solid #ddd;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.875rem;
}

.grid-body {
  flex-grow: 1;
}

.position-row {
  display: flex;
  border-bottom: 1px solid #e0e0e0;
  min-height: 44px;
}

.position-label {
  min-width: 80px;
  max-width: 80px;
  padding: 8px;
  font-weight: 600;
  background: #f0f0f0;
  border-right: 1px solid #ddd;
  display: flex;
  align-items: center;
  justify-content: center;
  position: sticky;
  left: 0;
  z-index: 2;
}

.position-cells {
  display: flex;
  flex: 1;
}

.grid-cell {
  min-width: 120px;
  max-width: 120px;
  border-right: 1px solid #e0e0e0;
}

.keyboard-hints {
  padding: 8px;
  background: rgba(0, 0, 0, 0.02);
  border-top: 1px solid #e0e0e0;
  text-align: center;
}

/* Drag and drop styles */
.position-row.sortable-chosen {
  background: rgba(25, 118, 210, 0.05);
}

.grid-cell.sortable-ghost {
  opacity: 0.3;
}

.grid-cell.sortable-drag {
  transform: rotate(2deg);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* Scrollbar styling */
.lineup-grid-container::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.lineup-grid-container::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.lineup-grid-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.lineup-grid-container::-webkit-scrollbar-thumb:hover {
  background: #a1a1a1;
}

/* Responsive design */
@media (max-width: 768px) {
  .inning-header {
    min-width: 100px;
    max-width: 100px;
    font-size: 0.75rem;
  }

  .grid-cell {
    min-width: 100px;
    max-width: 100px;
  }

  .keyboard-hints {
    display: none;
  }
}

/* Accessibility */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .lineup-cell {
    border-width: 2px;
  }

  .cell-focused {
    border-width: 3px;
  }

  .cell-player-highlighted {
    border-width: 3px;
  }
}

/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
  .grid-cell,
  .lineup-cell {
    transition: none;
  }

  .grid-cell.sortable-drag {
    transform: none;
  }
}
</style>