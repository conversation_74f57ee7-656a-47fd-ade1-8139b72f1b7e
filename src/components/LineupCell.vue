<template>
  <div
    ref="cellRef"
    class="lineup-cell"
    :class="cellClasses"
    :tabindex="isFocusable ? 0 : -1"
    :aria-label="cellAriaLabel"
    :aria-selected="isSelected"
    :aria-current="isFocused ? 'true' : undefined"
    :aria-describedby="isSelected ? 'player-description' : undefined"
    role="gridcell"
    aria-live="polite"
    @click="handleClick"
    @keydown="handleKeydown"
    @focus="handleFocus"
    @blur="handleBlur"
  >
    <v-autocomplete
      v-if="isEditing"
      ref="autocompleteRef"
      v-model="editValue"
      :items="filteredPlayers"
      item-title="displayName"
      item-value="id"
      variant="outlined"
      density="compact"
      hide-details
      auto-select-first
      clearable
      placeholder="Type player name..."
      class="cell-editor"
      @keydown.escape.stop="cancelEdit"
      @keydown.enter.stop="confirmEdit"
      @update:model-value="confirmEdit"
      @blur="handleEditorBlur"
    />
    <div v-else class="cell-content">
      <span v-if="displayPlayer" class="player-name">
        {{ displayPlayer.firstName }} {{ displayPlayer.lastName || '' }}
      </span>
      <span v-else class="placeholder">-</span>
    </div>

    <!-- Focus indicator -->
    <div v-if="isFocused" class="focus-indicator" />

    <!-- Selection indicator -->
    <div v-if="isSelected && !isFocused" class="selection-indicator" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick, watch } from 'vue'
import type { Player } from '../types'

interface Props {
  gameId: string
  inning: number
  positionKey: string
  playerId?: string | null
  players: Player[]
  isFocused?: boolean
  isSelected?: boolean
  selectedPlayerId?: string | null
  isEditing?: boolean
  isFocusable?: boolean
}

interface Emits {
  (e: 'update:playerId', value: string | null): void
  (e: 'focus', data: { gameId: string, inning: number, positionKey: string }): void
  (e: 'edit-start'): void
  (e: 'edit-end'): void
  (e: 'click', event: MouseEvent): void
  (e: 'keydown', event: KeyboardEvent): void
}

const props = withDefaults(defineProps<Props>(), {
  playerId: null,
  isFocused: false,
  isSelected: false,
  selectedPlayerId: null,
  isEditing: false,
  isFocusable: true
})

const emit = defineEmits<Emits>()

const cellRef = ref<HTMLElement>()
const autocompleteRef = ref<any>()
const editValue = ref<string | null>(null)

const displayPlayer = computed(() => {
  if (!props.playerId) return null
  return props.players.find(p => p.id === props.playerId)
})

const filteredPlayers = computed(() => {
  return props.players.map(player => ({
    id: player.id,
    firstName: player.firstName,
    lastName: player.lastName || '',
    throws: player.throws,
    bats: player.bats,
    displayName: `${player.firstName} ${player.lastName || ''}`.trim()
  }))
})

const cellClasses = computed(() => {
  return {
    'cell-focused': props.isFocused,
    'cell-selected': props.isSelected,
    'cell-editing': props.isEditing,
    'cell-occupied': !!props.playerId,
    'cell-player-highlighted': props.selectedPlayerId === props.playerId && props.playerId,
    'cell-empty': !props.playerId
  }
})

const cellAriaLabel = computed(() => {
  const position = props.positionKey
  const inning = props.inning
  const player = displayPlayer.value

  if (player) {
    return `${position} position, inning ${inning}, assigned to ${player.firstName} ${player.lastName || ''}`
  }
  return `${position} position, inning ${inning}, empty`
})

const handleClick = (event: MouseEvent) => {
  if (event.ctrlKey || event.metaKey) {
    // Handle multi-select via parent component
    emit('click', event)
    return
  }

  // Focus the cell
  emit('focus', {
    gameId: props.gameId,
    inning: props.inning,
    positionKey: props.positionKey
  })

  emit('click', event)
}

const handleKeydown = (event: KeyboardEvent) => {
  if (props.isEditing) return

  switch (event.key) {
    case 'Enter':
      event.preventDefault()
      startEdit()
      break
    case 'Delete':
    case 'Backspace':
      event.preventDefault()
      clearCell()
      break
    default:
      // Let parent handle navigation keys
      emit('keydown', event)
  }
}

const handleFocus = () => {
  if (!props.isFocused) {
    emit('focus', {
      gameId: props.gameId,
      inning: props.inning,
      positionKey: props.positionKey
    })
  }
}

const handleBlur = () => {
  // Don't blur if we're entering edit mode
  if (!props.isEditing) {
    // Let parent handle blur logic
  }
}

const startEdit = () => {
  editValue.value = props.playerId
  emit('edit-start')

  nextTick(() => {
    if (autocompleteRef.value) {
      autocompleteRef.value.focus()
    }
  })
}

const confirmEdit = () => {
  emit('update:playerId', editValue.value)
  emit('edit-end')

  nextTick(() => {
    if (cellRef.value) {
      cellRef.value.focus()
    }
  })
}

const cancelEdit = () => {
  editValue.value = props.playerId
  emit('edit-end')

  nextTick(() => {
    if (cellRef.value) {
      cellRef.value.focus()
    }
  })
}

const handleEditorBlur = () => {
  // Small delay to allow for autocomplete selection
  setTimeout(() => {
    if (props.isEditing) {
      confirmEdit()
    }
  }, 150)
}

const clearCell = () => {
  emit('update:playerId', null)
}

// Watch for focus changes to ensure proper DOM focus
watch(() => props.isFocused, (focused) => {
  if (focused && cellRef.value && document.activeElement !== cellRef.value) {
    cellRef.value.focus()
  }
})

defineExpose({
  focus: () => {
    if (cellRef.value) {
      cellRef.value.focus()
    }
  },
  startEdit,
  confirmEdit,
  cancelEdit
})
</script>

<style scoped>
.lineup-cell {
  position: relative;
  min-height: 40px;
  min-width: 120px;
  border: 1px solid #e0e0e0;
  background: white;
  cursor: pointer;
  transition: all 0.2s ease;
  outline: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.lineup-cell:hover {
  background: #f5f5f5;
  border-color: #bdbdbd;
}

.cell-focused {
  border-color: #1976d2 !important;
  border-width: 2px !important;
  z-index: 2;
}

.cell-selected {
  background: #e3f2fd !important;
}

.cell-editing {
  border-color: #4caf50 !important;
  border-width: 2px !important;
}

.cell-occupied {
  background: #f0f8f0;
}

.cell-player-highlighted {
  background: #fff3e0 !important;
  border-color: #ff9800 !important;
}

.cell-empty .placeholder {
  color: #9e9e9e;
  font-style: italic;
}

.cell-content {
  width: 100%;
  text-align: center;
  padding: 8px;
}

.player-name {
  font-weight: 500;
  font-size: 0.875rem;
  color: #333;
}

.cell-editor {
  width: 100%;
}

.cell-editor :deep(.v-field) {
  min-height: 36px;
}

.focus-indicator {
  position: absolute;
  top: 2px;
  left: 2px;
  right: 2px;
  bottom: 2px;
  border: 2px solid #1976d2;
  pointer-events: none;
  z-index: 1;
}

.selection-indicator {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(25, 118, 210, 0.1);
  border: 1px solid rgba(25, 118, 210, 0.3);
  pointer-events: none;
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .lineup-cell {
    transition: none;
  }
}
</style>