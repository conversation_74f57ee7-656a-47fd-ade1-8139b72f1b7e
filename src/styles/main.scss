// Main SCSS file that imports all other styles

// Import Vuetify settings first
@import 'settings';
@import 'variables';

// Import Roboto font
@import url('https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap');

// Global styles
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  font-size: $font-size-root;
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  font-family: $body-font-family, sans-serif;
  font-weight: 400;
  color: rgba(0, 0, 0, 0.87);
  background-color: var(--v-theme-background);
}

// Utility classes
.text-uppercase {
  text-transform: uppercase;
}

.text-lowercase {
  text-transform: lowercase;
}

.text-capitalize {
  text-transform: capitalize;
}

.text-nowrap {
  white-space: nowrap;
}

// Layout
.container {
  max-width: 1440px;
  margin: 0 auto;
  padding: 0 16px;
  width: 100%;
}

// Responsive helpers
@each $breakpoint, $width in $breakpoints {
  @media (min-width: $width) {
    .hidden-#{$breakpoint} {
      display: none !important;
    }
  }
}

// Vuetify overrides
.v-application {
  font-family: $body-font-family, sans-serif !important;
  background-color: var(--v-theme-background) !important;
}

.v-application--wrap {
  min-height: 100vh;
}

// Custom scrollbar
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #555;
}
