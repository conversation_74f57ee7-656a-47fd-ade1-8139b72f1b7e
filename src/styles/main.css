/* Global styles */
:root {
  --v-theme-background: #f5f5f5;
  --v-theme-surface: #ffffff;
  --v-theme-primary: #1976D2;
  --v-theme-secondary: #424242;
  --v-theme-accent: #82B1FF;
  --v-theme-error: #FF5252;
  --v-theme-info: #2196F3;
  --v-theme-success: #4CAF50;
  --v-theme-warning: #FFC107;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  font-size: 14px;
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  font-family: 'Roboto', sans-serif;
  font-weight: 400;
  color: rgba(0, 0, 0, 0.87);
  background-color: var(--v-theme-background);
}

/* Utility classes */
.text-uppercase {
  text-transform: uppercase;
}
