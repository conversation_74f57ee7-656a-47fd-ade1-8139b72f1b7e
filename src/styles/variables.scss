// Custom variables for the application

// Colors
$primary: #1976D2;
$secondary: #424242;
$accent: #82B1FF;
$error: #FF5252;
$info: #2196F3;
$success: #4CAF50;
$warning: #FFC107;

// Spacing
$spacer: 8px;
$spacers: (
  0: 0,
  1: $spacer * 0.25,
  2: $spacer * 0.5,
  3: $spacer,
  4: $spacer * 1.5,
  5: $spacer * 3,
);

// Breakpoints
$breakpoints: (
  'xs': 0,
  'sm': 600px,
  'md': 960px,
  'lg': 1280px - 16px,
  'xl': 1920px - 16px,
) !default;

// Typography
$font-sizes: (
  'h1': 2.5rem,
  'h2': 2rem,
  'h3': 1.75rem,
  'h4': 1.5rem,
  'h5': 1.25rem,
  'h6': 1rem,
  'body-1': 1rem,
  'body-2': 0.875rem,
  'caption': 0.75rem,
  'overline': 0.625rem,
);

// Transitions
$transition: (
  fast-out-slow-in: cubic-bezier(0.4, 0, 0.2, 1),
  linear-out-slow-in: cubic-bezier(0, 0, 0.2, 1),
  fast-out-linear-in: cubic-bezier(0.4, 0, 1, 1),
  ease-in-out: cubic-bezier(0.4, 0, 0.6, 1),
  fast-in-fast-out: cubic-bezier(0.25, 0.8, 0.25, 1),
  swing: cubic-bezier(0.25, 0.8, 0.5, 1)
);

// Z-index
$z-index: (
  'navigation-drawer': 1000,
  'app-bar': 1100,
  'dialog': 1300,
  'tooltip': 1400,
  'snackbar': 1500,
  'menu': 1600,
  'modal': 1700,
  'toast': 1800,
);
