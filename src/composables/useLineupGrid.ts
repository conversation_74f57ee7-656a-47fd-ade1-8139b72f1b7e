import { ref, reactive, computed, nextTick } from 'vue'
import { v4 as uuidv4 } from 'uuid'
import { useDebounce, useBatchUpdates, useRafUpdates } from './usePerformance'
import type {
  Player,
  Game,
  GridCell,
  FocusPosition,
  ClipboardData,
  UndoRedoAction,
  NavigationDirection,
  SelectionState
} from '../types'
import { POSITIONS } from '../types'

export function useLineupGrid(game: Game, players: Player[]) {
  // Performance utilities
  const { batchUpdate } = useBatchUpdates()
  const { scheduleUpdate } = useRafUpdates()

  // Debounced auto-save (simulate saving to storage)
  const debouncedSave = useDebounce(() => {
    // Here we would save to localStorage, IndexedDB, or API
    console.log('Auto-saving lineup data...', Array.from(gridData.entries()))
  }, 1000)

  // Grid state
  const gridData = reactive<Map<string, string | null>>(new Map())
  const focusedCell = ref<FocusPosition | null>(null)
  const editingCell = ref<FocusPosition | null>(null)
  const selectedCells = ref<Set<string>>(new Set())
  const selectedPlayerId = ref<string | null>(null)
  const selectionState = ref<SelectionState>('none')

  // Clipboard and undo/redo
  const clipboardData = ref<ClipboardData | null>(null)
  const undoStack = ref<UndoRedoAction[]>([])
  const redoStack = ref<UndoRedoAction[]>([])
  const maxUndoSteps = 50

  // Utility functions
  const getCellKey = (inning: number, positionKey: string): string => {
    return `${game.id}:i${inning}:pos${positionKey}`
  }

  const parseCellKey = (key: string): FocusPosition | null => {
    const match = key.match(/^(.+):i(\d+):pos(.+)$/)
    if (!match) return null

    return {
      gameId: match[1],
      inning: parseInt(match[2]),
      positionKey: match[3]
    }
  }

  const getPlayerId = (inning: number, positionKey: string): string | null => {
    const key = getCellKey(inning, positionKey)
    return gridData.get(key) || null
  }

  const setPlayerId = (inning: number, positionKey: string, playerId: string | null): void => {
    const key = getCellKey(inning, positionKey)

    // Use batched update for performance
    batchUpdate(`set-${key}`, () => {
      if (playerId) {
        gridData.set(key, playerId)
      } else {
        gridData.delete(key)
      }
    })
  }

  // Grid initialization
  const initializeGrid = (): void => {
    gridData.clear()
    // Grid starts empty - cells are created on demand
  }

  // Focus management
  const setFocusedCell = (position: FocusPosition): void => {
    // Use RAF for smooth focus updates
    scheduleUpdate(() => {
      focusedCell.value = position

      // Update selected player if clicking on an occupied cell
      const playerId = getPlayerId(position.inning, position.positionKey)
      if (playerId && selectionState.value !== 'multi') {
        selectedPlayerId.value = playerId
        highlightPlayerOccurrences(playerId)
      }
    })
  }

  const clearFocus = (): void => {
    focusedCell.value = null
  }

  // Cell editing
  const startEditing = (position: FocusPosition): void => {
    editingCell.value = position
  }

  const stopEditing = (): void => {
    editingCell.value = null
  }

  const isEditing = (inning: number, positionKey: string): boolean => {
    if (!editingCell.value) return false
    return editingCell.value.inning === inning && editingCell.value.positionKey === positionKey
  }

  // Player selection and highlighting
  const highlightPlayerOccurrences = (playerId: string): void => {
    selectedPlayerId.value = playerId
  }

  const clearPlayerHighlight = (): void => {
    selectedPlayerId.value = null
  }

  const getPlayerOccurrences = (playerId: string): FocusPosition[] => {
    const occurrences: FocusPosition[] = []

    for (let inning = 1; inning <= game.inningCount; inning++) {
      for (const position of POSITIONS) {
        if (getPlayerId(inning, position) === playerId) {
          occurrences.push({
            gameId: game.id,
            inning,
            positionKey: position
          })
        }
      }
    }

    return occurrences
  }

  // Multi-selection
  const toggleCellSelection = (inning: number, positionKey: string): void => {
    const key = getCellKey(inning, positionKey)

    if (selectedCells.value.has(key)) {
      selectedCells.value.delete(key)
    } else {
      selectedCells.value.add(key)
    }

    selectionState.value = selectedCells.value.size > 0 ? 'multi' : 'none'
  }

  const clearSelection = (): void => {
    selectedCells.value.clear()
    selectionState.value = 'none'
  }

  const isCellSelected = (inning: number, positionKey: string): boolean => {
    const key = getCellKey(inning, positionKey)
    return selectedCells.value.has(key)
  }

  // Keyboard navigation
  const navigate = (direction: NavigationDirection): void => {
    if (!focusedCell.value) return

    const { inning, positionKey } = focusedCell.value
    const currentPositionIndex = POSITIONS.indexOf(positionKey as any)

    let newInning = inning
    let newPositionIndex = currentPositionIndex

    switch (direction) {
      case 'up':
        newPositionIndex = Math.max(0, currentPositionIndex - 1)
        break
      case 'down':
        newPositionIndex = Math.min(POSITIONS.length - 1, currentPositionIndex + 1)
        break
      case 'left':
        newInning = Math.max(1, inning - 1)
        break
      case 'right':
        newInning = Math.min(game.inningCount, inning + 1)
        break
      case 'home':
        newInning = 1
        break
      case 'end':
        newInning = game.inningCount
        break
    }

    if (newInning !== inning || newPositionIndex !== currentPositionIndex) {
      setFocusedCell({
        gameId: game.id,
        inning: newInning,
        positionKey: POSITIONS[newPositionIndex]
      })
    }
  }

  // Clipboard operations
  const copyCell = (): void => {
    if (!focusedCell.value || selectionState.value === 'multi') return

    const playerId = getPlayerId(focusedCell.value.inning, focusedCell.value.positionKey)

    clipboardData.value = {
      playerId,
      operation: 'copy',
      timestamp: Date.now()
    }
  }

  const cutCell = (): void => {
    if (!focusedCell.value || selectionState.value === 'multi') return

    const playerId = getPlayerId(focusedCell.value.inning, focusedCell.value.positionKey)

    clipboardData.value = {
      playerId,
      operation: 'cut',
      timestamp: Date.now()
    }

    // Clear the cell after cutting
    const action = createUndoAction('clear', [
      {
        gameId: game.id,
        inning: focusedCell.value.inning,
        positionKey: focusedCell.value.positionKey,
        playerId: null
      }
    ], [
      {
        gameId: game.id,
        inning: focusedCell.value.inning,
        positionKey: focusedCell.value.positionKey,
        playerId
      }
    ], 'Cut player from cell')

    setPlayerId(focusedCell.value.inning, focusedCell.value.positionKey, null)
    pushUndoAction(action)
  }

  const pasteCell = (): void => {
    if (!clipboardData.value || !focusedCell.value) return

    const { playerId } = clipboardData.value

    if (selectionState.value === 'multi') {
      // Paste into all selected cells
      const cells: GridCell[] = []
      const previousState: GridCell[] = []

      for (const key of selectedCells.value) {
        const position = parseCellKey(key)
        if (position) {
          const oldPlayerId = getPlayerId(position.inning, position.positionKey)

          cells.push({
            gameId: position.gameId,
            inning: position.inning,
            positionKey: position.positionKey,
            playerId
          })

          previousState.push({
            gameId: position.gameId,
            inning: position.inning,
            positionKey: position.positionKey,
            playerId: oldPlayerId
          })

          setPlayerId(position.inning, position.positionKey, playerId)
        }
      }

      const action = createUndoAction('bulk', cells, previousState, 'Paste into multiple cells')
      pushUndoAction(action)
      clearSelection()
    } else {
      // Paste into focused cell
      const oldPlayerId = getPlayerId(focusedCell.value.inning, focusedCell.value.positionKey)

      const action = createUndoAction('assign', [
        {
          gameId: game.id,
          inning: focusedCell.value.inning,
          positionKey: focusedCell.value.positionKey,
          playerId
        }
      ], [
        {
          gameId: game.id,
          inning: focusedCell.value.inning,
          positionKey: focusedCell.value.positionKey,
          playerId: oldPlayerId
        }
      ], 'Paste player into cell')

      setPlayerId(focusedCell.value.inning, focusedCell.value.positionKey, playerId)
      pushUndoAction(action)
    }
  }

  // Cell assignment
  const assignPlayer = (inning: number, positionKey: string, playerId: string | null): void => {
    const oldPlayerId = getPlayerId(inning, positionKey)

    if (oldPlayerId === playerId) return

    const action = createUndoAction('assign', [
      {
        gameId: game.id,
        inning,
        positionKey,
        playerId
      }
    ], [
      {
        gameId: game.id,
        inning,
        positionKey,
        playerId: oldPlayerId
      }
    ], `${playerId ? 'Assign' : 'Clear'} player ${playerId ? 'to' : 'from'} ${positionKey} inning ${inning}`)

    setPlayerId(inning, positionKey, playerId)
    pushUndoAction(action)
    debouncedSave() // Trigger auto-save
  }

  const clearCell = (inning: number, positionKey: string): void => {
    assignPlayer(inning, positionKey, null)
  }

  // Undo/Redo functionality
  const createUndoAction = (
    type: UndoRedoAction['type'],
    cells: GridCell[],
    previousState: GridCell[],
    description: string
  ): UndoRedoAction => {
    return {
      id: uuidv4(),
      type,
      timestamp: Date.now(),
      data: {
        cells,
        previousState
      },
      description
    }
  }

  const pushUndoAction = (action: UndoRedoAction): void => {
    undoStack.value.push(action)
    redoStack.value = [] // Clear redo stack when new action is performed

    // Limit undo stack size
    if (undoStack.value.length > maxUndoSteps) {
      undoStack.value.shift()
    }
  }

  const undo = (): boolean => {
    const action = undoStack.value.pop()
    if (!action) return false

    // Restore previous state
    for (const cell of action.data.previousState) {
      setPlayerId(cell.inning, cell.positionKey, cell.playerId || null)
    }

    redoStack.value.push(action)
    return true
  }

  const redo = (): boolean => {
    const action = redoStack.value.pop()
    if (!action) return false

    // Apply the action again
    for (const cell of action.data.cells) {
      setPlayerId(cell.inning, cell.positionKey, cell.playerId || null)
    }

    undoStack.value.push(action)
    return true
  }

  // Computed properties with memoization
  const canUndo = computed(() => undoStack.value.length > 0)
  const canRedo = computed(() => redoStack.value.length > 0)

  // Memoized player count calculation for performance
  const getPlayerCount = computed(() => {
    const counts: Record<string, number> = {}
    const entries = Array.from(gridData.entries())

    // Use a single pass for better performance
    for (let i = 0; i < entries.length; i++) {
      const [, playerId] = entries[i]
      if (playerId) {
        counts[playerId] = (counts[playerId] || 0) + 1
      }
    }

    return counts
  })

  // Initialize grid
  initializeGrid()

  return {
    // State
    focusedCell,
    editingCell,
    selectedCells,
    selectedPlayerId,
    selectionState,
    clipboardData,

    // Methods
    getPlayerId,
    setPlayerId,
    setFocusedCell,
    clearFocus,
    startEditing,
    stopEditing,
    isEditing,
    highlightPlayerOccurrences,
    clearPlayerHighlight,
    getPlayerOccurrences,
    toggleCellSelection,
    clearSelection,
    isCellSelected,
    navigate,
    copyCell,
    cutCell,
    pasteCell,
    assignPlayer,
    clearCell,
    undo,
    redo,

    // Computed
    canUndo,
    canRedo,
    getPlayerCount
  }
}