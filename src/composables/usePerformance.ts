import { ref, nextTick } from 'vue'

// Debounce utility
export function useDebounce<T extends (...args: any[]) => any>(
  fn: T,
  delay: number
): (...args: Parameters<T>) => void {
  let timeoutId: number | undefined

  return (...args: Parameters<T>) => {
    clearTimeout(timeoutId)
    timeoutId = window.setTimeout(() => fn(...args), delay)
  }
}

// Throttle utility
export function useThrottle<T extends (...args: any[]) => any>(
  fn: T,
  delay: number
): (...args: Parameters<T>) => void {
  let isThrottled = false

  return (...args: Parameters<T>) => {
    if (isThrottled) return

    fn(...args)
    isThrottled = true
    setTimeout(() => {
      isThrottled = false
    }, delay)
  }
}

// RAF-based smooth updates
export function useRafUpdates() {
  const pendingUpdates = new Set<() => void>()
  let rafId: number | null = null

  const scheduleUpdate = (updateFn: () => void) => {
    pendingUpdates.add(updateFn)

    if (!rafId) {
      rafId = requestAnimationFrame(() => {
        for (const update of pendingUpdates) {
          update()
        }
        pendingUpdates.clear()
        rafId = null
      })
    }
  }

  const cancelUpdates = () => {
    if (rafId) {
      cancelAnimationFrame(rafId)
      rafId = null
    }
    pendingUpdates.clear()
  }

  return {
    scheduleUpdate,
    cancelUpdates
  }
}

// Performance monitoring
export function usePerformanceMonitoring() {
  const metrics = ref({
    renderTime: 0,
    updateTime: 0,
    dragTime: 0,
    keyboardTime: 0
  })

  const measureTime = <T>(
    operation: () => T,
    metricKey: keyof typeof metrics.value
  ): T => {
    const start = performance.now()
    const result = operation()
    const end = performance.now()

    metrics.value[metricKey] = end - start

    return result
  }

  const measureAsyncTime = async <T>(
    operation: () => Promise<T>,
    metricKey: keyof typeof metrics.value
  ): Promise<T> => {
    const start = performance.now()
    const result = await operation()
    const end = performance.now()

    metrics.value[metricKey] = end - start

    return result
  }

  return {
    metrics,
    measureTime,
    measureAsyncTime
  }
}

// Memory usage monitoring
export function useMemoryMonitoring() {
  const getMemoryUsage = () => {
    if ('memory' in performance) {
      return {
        used: (performance as any).memory.usedJSHeapSize,
        total: (performance as any).memory.totalJSHeapSize,
        limit: (performance as any).memory.jsHeapSizeLimit
      }
    }
    return null
  }

  const monitorMemory = (intervalMs: number = 5000) => {
    const memoryStats = ref(getMemoryUsage())

    const interval = setInterval(() => {
      memoryStats.value = getMemoryUsage()
    }, intervalMs)

    const stop = () => clearInterval(interval)

    return {
      memoryStats,
      stop
    }
  }

  return {
    getMemoryUsage,
    monitorMemory
  }
}

// Intersection observer for lazy loading
export function useIntersectionObserver(
  callback: (entries: IntersectionObserverEntry[]) => void,
  options?: IntersectionObserverInit
) {
  let observer: IntersectionObserver | null = null

  const observe = (element: Element) => {
    if (!observer) {
      observer = new IntersectionObserver(callback, {
        threshold: 0.1,
        rootMargin: '50px',
        ...options
      })
    }
    observer.observe(element)
  }

  const unobserve = (element: Element) => {
    if (observer) {
      observer.unobserve(element)
    }
  }

  const disconnect = () => {
    if (observer) {
      observer.disconnect()
      observer = null
    }
  }

  return {
    observe,
    unobserve,
    disconnect
  }
}

// Optimized batch updates
export function useBatchUpdates() {
  const batchedUpdates = new Map<string, () => void>()
  const updateScheduled = ref(false)

  const batchUpdate = (key: string, updateFn: () => void) => {
    batchedUpdates.set(key, updateFn)

    if (!updateScheduled.value) {
      updateScheduled.value = true
      nextTick(() => {
        for (const [, update] of batchedUpdates) {
          update()
        }
        batchedUpdates.clear()
        updateScheduled.value = false
      })
    }
  }

  const cancelBatchUpdate = (key: string) => {
    batchedUpdates.delete(key)
  }

  const flushUpdates = async () => {
    if (batchedUpdates.size > 0) {
      for (const [, update] of batchedUpdates) {
        update()
      }
      batchedUpdates.clear()
      updateScheduled.value = false
      await nextTick()
    }
  }

  return {
    batchUpdate,
    cancelBatchUpdate,
    flushUpdates
  }
}