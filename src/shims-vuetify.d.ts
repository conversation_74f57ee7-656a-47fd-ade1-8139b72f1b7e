// Vuetify type declarations
declare module 'vuetify/lib/framework' {
  import { Vuetify } from 'vuetify'
  export default Vuetify
}

declare module 'vuetify' {
  import { DefineComponent } from 'vue'
  import { Vuetify } from 'vuetify/lib/framework'
  
  export * from 'vuetify/lib/framework'
  
  // Common Vuetify components
  const VApp: DefineComponent<{}, {}, any>
  const VBtn: DefineComponent<{}, {}, any>
  const VContainer: DefineComponent<{}, {}, any>
  const VRow: DefineComponent<{}, {}, any>
  const VCol: DefineComponent<{}, {}, any>
  const VCard: DefineComponent<{}, {}, any>
  const VCardTitle: DefineComponent<{}, {}, any>
  const VCardText: DefineComponent<{}, {}, any>
  const VTextField: DefineComponent<{}, {}, any>
  const VSelect: DefineComponent<{}, {}, any>
  const VCheckbox: DefineComponent<{}, {}, any>
  const VDialog: DefineComponent<{}, {}, any>
  const VToolbar: DefineComponent<{}, {}, any>
  const VToolbarTitle: DefineComponent<{}, {}, any>
  const VSpacer: DefineComponent<{}, {}, any>
  const VBtnToggle: DefineComponent<{}, {}, any>
  const VIcon: DefineComponent<{}, {}, any>
  const VList: DefineComponent<{}, {}, any>
  const VListItem: DefineComponent<{}, {}, any>
  const VListItemTitle: DefineComponent<{}, {}, any>
  const VListItemSubtitle: DefineComponent<{}, {}, any>
  const VDivider: DefineComponent<{}, {}, any>
  const VMenu: DefineComponent<{}, {}, any>
  const VProgressLinear: DefineComponent<{}, {}, any>
  const VSnackbar: DefineComponent<{}, {}, any>
  const VAutocomplete: DefineComponent<{}, {}, any>
  
  export {
    VApp, VBtn, VContainer, VRow, VCol, VCard, VCardTitle, VCardText,
    VTextField, VSelect, VCheckbox, VDialog, VToolbar, VToolbarTitle,
    VSpacer, VBtnToggle, VIcon, VList, VListItem, VListItemTitle,
    VListItemSubtitle, VDivider, VMenu, VProgressLinear, VSnackbar,
    VAutocomplete
  }
  
  export function createVuetify(config?: any): Vuetify
  
  // Add missing type exports
  export * from 'vuetify/components'
  export * from 'vuetify/directives'
  export * from 'vuetify/composables'
  export * from 'vuetify/labs/components'
  export * from 'vuetify/util/helpers'
  export * from 'vuetify/util/colors'
  export * from 'vuetify/util/colors/red'
  export * from 'vuetify/util/colors/pink'
  export * from 'vuetify/util/colors/purple'
  export * from 'vuetify/util/colors/deep-purple'
  export * from 'vuetify/util/colors/indigo'
  export * from 'vuetify/util/colors/blue'
  export * from 'vuetify/util/colors/light-blue'
  export * from 'vuetify/util/colors/cyan'
  export * from 'vuetify/util/colors/teal'
  export * from 'vuetify/util/colors/green'
  export * from 'vuetify/util/colors/light-green'
  export * from 'vuetify/util/colors/lime'
  export * from 'vuetify/util/colors/yellow'
  export * from 'vuetify/util/colors/amber'
  export * from 'vuetify/util/colors/orange'
  export * from 'vuetify/util/colors/deep-orange'
  export * from 'vuetify/util/colors/brown'
  export * from 'vuetify/util/colors/blue-grey'
  export * from 'vuetify/util/colors/grey'
  export * from 'vuetify/util/colors/shades'
  export * from 'vuetify/util/colors/red'
  export * from 'vuetify/util/colors/pink'
  export * from 'vuetify/util/colors/purple'
  export * from 'vuetify/util/colors/deep-purple'
  export * from 'vuetify/util/colors/indigo'
  export * from 'vuetify/util/colors/blue'
  export * from 'vuetify/util/colors/light-blue'
  export * from 'vuetify/util/colors/cyan'
  export * from 'vuetify/util/colors/teal'
  export * from 'vuetify/util/colors/green'
  export * from 'vuetify/util/colors/light-green'
  export * from 'vuetify/util/colors/lime'
  export * from 'vuetify/util/colors/yellow'
  export * from 'vuetify/util/colors/amber'
  export * from 'vuetify/util/colors/orange'
  export * from 'vuetify/util/colors/deep-orange'
  export * from 'vuetify/util/colors/brown'
  export * from 'vuetify/util/colors/blue-grey'
  export * from 'vuetify/util/colors/grey'
  export * from 'vuetify/util/colors/shades'
}

declare module 'vuetify/*' {
  import { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}
