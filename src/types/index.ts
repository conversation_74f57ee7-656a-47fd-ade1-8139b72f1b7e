// Type definitions for Lineup Lab application

export type ID = string

export interface Player {
  id: ID
  firstName: string
  lastName: string
  throws?: 'L' | 'R' | 'S'
  bats?: 'L' | 'R' | 'S'
  dob?: string | null
}

export interface Team {
  id: ID
  name: string
  season: string
  headCoach?: string
  assistantCoaches?: string[]
  notes?: string
  players: Player[]
  games: Game[]
}

export interface TeamPlayer {
  id: ID
  teamId: ID
  playerId: ID
  jerseyNumber?: string | number
  preferredPositions: string[]
  facePhotoId?: ID
}

export interface Photo {
  id: ID
  teamId: ID
  playerId?: ID | null
  filename?: string
  mimeType?: string
  blob?: Blob | string
  createdAt: string
}

export interface Game {
  id: ID
  teamId: ID
  date: string
  gameTime?: string
  arrivalTime?: string
  opponent: string
  homeAway: 'Home' | 'Away'
  address?: string
  notes?: string
  inningCount: number
}

export interface LineupCell {
  id: ID
  gameId: ID
  inning: number
  positionKey: string
  playerId?: ID | null
  createdAt: string
  updatedAt?: string
}

export interface GridCell {
  gameId: string
  inning: number
  positionKey: string
  playerId?: string | null
}

export interface FocusPosition {
  gameId: string
  inning: number
  positionKey: string
}

export interface ClipboardData {
  playerId: string | null
  operation: 'copy' | 'cut'
  timestamp: number
}

export interface UndoRedoAction {
  id: string
  type: 'assign' | 'clear' | 'move' | 'bulk'
  timestamp: number
  data: {
    cells: GridCell[]
    previousState: GridCell[]
  }
  description: string
}

export interface DragDropData {
  playerId: string
  sourceType: 'roster' | 'cell'
  sourcePosition?: FocusPosition
  operation: 'move' | 'copy'
}

// Standard baseball positions
export const POSITIONS = [
  'P',    // Pitcher
  'C',    // Catcher
  '1B',   // First Base
  '2B',   // Second Base
  '3B',   // Third Base
  'SS',   // Shortstop
  'LF',   // Left Field
  'CF',   // Center Field
  'RF',   // Right Field
  'DH'    // Designated Hitter
] as const

export type Position = typeof POSITIONS[number]

// Player selection states
export type SelectionState = 'none' | 'single' | 'multi'

// Grid navigation directions
export type NavigationDirection = 'up' | 'down' | 'left' | 'right' | 'home' | 'end'

// Validation result for drag operations
export interface DropValidation {
  isValid: boolean
  message?: string
  requiresConfirmation?: boolean
}