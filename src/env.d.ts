/// <reference types="vite/client" />
/// <reference types="vuetify" />

// This file provides type declarations for Vuetify
declare module 'vuetify/lib/framework' {
  import { Vuetify } from 'vuetify'
  export default Vuetify
}

declare module 'vuetify' {
  import { DefineComponent } from 'vue'
  import { Vuetify } from 'vuetify'
  
  export * from 'vuetify/lib/framework'
  
  // Common Vuetify components
  const VApp: DefineComponent<{}, {}, any>
  const VBtn: DefineComponent<{}, {}, any>
  const VContainer: DefineComponent<{}, {}, any>
  const VRow: DefineComponent<{}, {}, any>
  const VCol: DefineComponent<{}, {}, any>
  const VCard: DefineComponent<{}, {}, any>
  const VCardTitle: DefineComponent<{}, {}, any>
  const VCardText: DefineComponent<{}, {}, any>
  const VTextField: DefineComponent<{}, {}, any>
  const VSelect: DefineComponent<{}, {}, any>
  const VCheckbox: DefineComponent<{}, {}, any>
  const VDialog: DefineComponent<{}, {}, any>
  const VToolbar: DefineComponent<{}, {}, any>
  const VToolbarTitle: DefineComponent<{}, {}, any>
  const VSpacer: DefineComponent<{}, {}, any>
  const VBtnToggle: DefineComponent<{}, {}, any>
  const VIcon: DefineComponent<{}, {}, any>
  const VList: DefineComponent<{}, {}, any>
  const VListItem: DefineComponent<{}, {}, any>
  const VListItemTitle: DefineComponent<{}, {}, any>
  const VListItemSubtitle: DefineComponent<{}, {}, any>
  const VDivider: DefineComponent<{}, {}, any>
  const VMenu: DefineComponent<{}, {}, any>
  const VProgressLinear: DefineComponent<{}, {}, any>
  const VSnackbar: DefineComponent<{}, {}, any>
  const VAutocomplete: DefineComponent<{}, {}, any>
  
  export {
    VApp, VBtn, VContainer, VRow, VCol, VCard, VCardTitle, VCardText,
    VTextField, VSelect, VCheckbox, VDialog, VToolbar, VToolbarTitle,
    VSpacer, VBtnToggle, VIcon, VList, VListItem, VListItemTitle,
    VListItemSubtitle, VDivider, VMenu, VProgressLinear, VSnackbar,
    VAutocomplete
  }
  
  export function createVuetify(config?: any): Vuetify
}

declare module 'vuetify/*' {
  import { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}
