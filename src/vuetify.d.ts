// This file provides type declarations for Vuetify
declare module 'vuetify/lib/framework' {
  import { Vuetify } from 'vuetify'
  export default Vuetify
}

declare module 'vuetify' {
  import { DefineComponent } from 'vue'
  export * from 'vuetify/lib/framework'
  
  const VApp: DefineComponent<{}, {}, any>
  const VBtn: DefineComponent<{}, {}, any>
  // Add other Vuetify components you use
  export { VApp, VBtn }
  
  export function createVuetify(config?: any): any
}
