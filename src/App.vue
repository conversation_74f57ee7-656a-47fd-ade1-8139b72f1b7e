<script setup lang="ts">
// This starter template is using Vue 3 <script setup> SFCs
// Check out https://vuejs.org/api/sfc-script-setup.html#script-setup
import { onMounted, ref, computed } from 'vue'
import { useTheme } from 'vuetify'
import LineupGrid from './components/LineupGrid.vue'
import DraggableRoster from './components/DraggableRoster.vue'
import type { Team, Game } from './types'

// Theme
const theme = useTheme()

// State
const selectedTeam = ref<Team | null>(null)
const selectedGame = ref<Game | null>(null)

// Mock data for three teams
const teams = ref<Team[]>([
  {
    id: '1',
    name: 'Wildcats 12U',
    season: 'Fall 2025',
    headCoach: '<PERSON>',
    players: [
      { id: '1-1', firstName: '<PERSON>', lastName: '<PERSON>', throws: 'R', bats: 'R' },
      { id: '1-2', firstName: '<PERSON>', lastName: '<PERSON>', throws: 'L', bats: 'L' },
      { id: '1-3', firstName: '<PERSON>', lastName: '<PERSON>', throws: 'R', bats: 'S' },
      { id: '1-4', firstName: '<PERSON>', lastName: '<PERSON>', throws: 'R', bats: 'R' },
      { id: '1-5', firstName: 'Ethan', lastName: 'Davis', throws: 'L', bats: 'R' },
      { id: '1-6', firstName: 'Felix', lastName: 'Garcia', throws: 'R', bats: 'L' },
      { id: '1-7', firstName: 'Gabriel', lastName: 'Brown', throws: 'R', bats: 'R' },
      { id: '1-8', firstName: 'Hunter', lastName: 'Miller', throws: 'R', bats: 'R' },
      { id: '1-9', firstName: 'Ian', lastName: 'Anderson', throws: 'L', bats: 'S' }
    ],
    games: [
      {
        id: '1-g1',
        teamId: '1',
        date: '2025-09-15',
        gameTime: '6:00 PM',
        arrivalTime: '5:30 PM',
        opponent: 'Lions 12U',
        homeAway: 'Home',
        address: '123 Baseball Field Rd, Hometown, PA',
        notes: 'Season opener - full lineup expected',
        inningCount: 12
      },
      {
        id: '1-g2',
        teamId: '1',
        date: '2025-09-22',
        gameTime: '4:00 PM',
        arrivalTime: '3:30 PM',
        opponent: 'Panthers 12U',
        homeAway: 'Away',
        address: '456 Sports Complex Dr, Visiting Town, PA',
        inningCount: 12
      }
    ]
  },
  {
    id: '2',
    name: 'Eagles 13U',
    season: 'Fall 2025',
    headCoach: 'Coach Smith',
    players: [
      { id: '2-1', firstName: 'Aaron', lastName: 'Jackson', throws: 'R', bats: 'R' },
      { id: '2-2', firstName: 'Blake', lastName: 'White', throws: 'R', bats: 'L' },
      { id: '2-3', firstName: 'Connor', lastName: 'Taylor', throws: 'L', bats: 'L' },
      { id: '2-4', firstName: 'Derek', lastName: 'Thomas', throws: 'R', bats: 'R' },
      { id: '2-5', firstName: 'Evan', lastName: 'Harris', throws: 'R', bats: 'S' },
      { id: '2-6', firstName: 'Finn', lastName: 'Clark', throws: 'L', bats: 'R' },
      { id: '2-7', firstName: 'Gavin', lastName: 'Lewis', throws: 'R', bats: 'R' },
      { id: '2-8', firstName: 'Henry', lastName: 'Walker', throws: 'R', bats: 'L' },
      { id: '2-9', firstName: 'Isaac', lastName: 'Hall', throws: 'L', bats: 'L' },
      { id: '2-10', firstName: 'Jake', lastName: 'Allen', throws: 'R', bats: 'R' },
      { id: '2-11', firstName: 'Kyle', lastName: 'Young', throws: 'R', bats: 'S' },
      { id: '2-12', firstName: 'Logan', lastName: 'King', throws: 'L', bats: 'R' }
    ],
    games: [
      {
        id: '2-g1',
        teamId: '2',
        date: '2025-09-18',
        gameTime: '7:00 PM',
        arrivalTime: '6:30 PM',
        opponent: 'Hawks 13U',
        homeAway: 'Home',
        address: '789 Diamond Ave, Eagle City, PA',
        notes: 'Night game - bring lights',
        inningCount: 14
      },
      {
        id: '2-g2',
        teamId: '2',
        date: '2025-09-25',
        gameTime: '5:30 PM',
        arrivalTime: '5:00 PM',
        opponent: 'Wolves 13U',
        homeAway: 'Away',
        address: '321 Field St, Wolf Town, PA',
        inningCount: 14
      },
      {
        id: '2-g3',
        teamId: '2',
        date: '2025-10-02',
        gameTime: '3:00 PM',
        arrivalTime: '2:30 PM',
        opponent: 'Bears 13U',
        homeAway: 'Home',
        address: '789 Diamond Ave, Eagle City, PA',
        inningCount: 14
      }
    ]
  },
  {
    id: '3',
    name: 'Tigers 14U',
    season: 'Fall 2025',
    headCoach: 'Coach Williams',
    players: [
      { id: '3-1', firstName: 'Adrian', lastName: 'Wright', throws: 'R', bats: 'R' },
      { id: '3-2', firstName: 'Brady', lastName: 'Lopez', throws: 'L', bats: 'L' },
      { id: '3-3', firstName: 'Caleb', lastName: 'Hill', throws: 'R', bats: 'R' },
      { id: '3-4', firstName: 'Daniel', lastName: 'Scott', throws: 'R', bats: 'S' },
      { id: '3-5', firstName: 'Eli', lastName: 'Green', throws: 'L', bats: 'L' },
      { id: '3-6', firstName: 'Frank', lastName: 'Adams', throws: 'R', bats: 'R' },
      { id: '3-7', firstName: 'George', lastName: 'Baker', throws: 'R', bats: 'L' },
      { id: '3-8', firstName: 'Henry', lastName: 'Gonzalez', throws: 'L', bats: 'R' },
      { id: '3-9', firstName: 'Ivan', lastName: 'Nelson', throws: 'R', bats: 'R' },
      { id: '3-10', firstName: 'Jack', lastName: 'Carter', throws: 'R', bats: 'S' },
      { id: '3-11', firstName: 'Kevin', lastName: 'Mitchell', throws: 'L', bats: 'L' },
      { id: '3-12', firstName: 'Liam', lastName: 'Perez', throws: 'R', bats: 'R' },
      { id: '3-13', firstName: 'Mason', lastName: 'Roberts', throws: 'R', bats: 'L' },
      { id: '3-14', firstName: 'Noah', lastName: 'Turner', throws: 'L', bats: 'S' },
      { id: '3-15', firstName: 'Owen', lastName: 'Phillips', throws: 'R', bats: 'R' }
    ],
    games: [
      {
        id: '3-g1',
        teamId: '3',
        date: '2025-09-20',
        gameTime: '6:30 PM',
        arrivalTime: '6:00 PM',
        opponent: 'Sharks 14U',
        homeAway: 'Away',
        address: '654 Stadium Blvd, Shark City, PA',
        notes: 'Championship qualifier game',
        inningCount: 16
      },
      {
        id: '3-g2',
        teamId: '3',
        date: '2025-09-27',
        gameTime: '4:30 PM',
        arrivalTime: '4:00 PM',
        opponent: 'Cardinals 14U',
        homeAway: 'Home',
        address: '987 Tiger Field Way, Tiger Town, PA',
        inningCount: 16
      },
      {
        id: '3-g3',
        teamId: '3',
        date: '2025-10-04',
        gameTime: '1:00 PM',
        arrivalTime: '12:30 PM',
        opponent: 'Bulldogs 14U',
        homeAway: 'Away',
        address: '159 Bulldog Park Dr, Dog City, PA',
        notes: 'Afternoon game - early arrival',
        inningCount: 16
      },
      {
        id: '3-g4',
        teamId: '3',
        date: '2025-10-11',
        gameTime: '7:30 PM',
        arrivalTime: '7:00 PM',
        opponent: 'Mustangs 14U',
        homeAway: 'Home',
        address: '987 Tiger Field Way, Tiger Town, PA',
        notes: 'Senior night game',
        inningCount: 16
      }
    ]
  }
])

// Methods
const selectTeam = (team: Team) => {
  selectedTeam.value = team
  selectedGame.value = null
}

const selectGame = (game: Game) => {
  selectedGame.value = game
}

const goBackToTeams = () => {
  selectedTeam.value = null
  selectedGame.value = null
}

const goBackToGames = () => {
  selectedGame.value = null
}

// Lineup state
const selectedPlayerId = ref<string | null>(null)
const lineupGridRef = ref<InstanceType<typeof LineupGrid> | null>(null)
const isDragging = ref(false)

// Player selection and highlighting
const handlePlayerSelect = (playerId: string) => {
  selectedPlayerId.value = playerId

  // Highlight player in grid
  if (lineupGridRef.value) {
    lineupGridRef.value.highlightPlayer(playerId)
  }
}

// Drag and drop handling
const currentDragData = ref<any>(null)

const handleDragStart = (data?: any) => {
  isDragging.value = true
  currentDragData.value = data
}

const handleDragEnd = () => {
  isDragging.value = false
  currentDragData.value = null
}

// Cell change handling
const handleCellChange = (data: { inning: number, position: string, playerId: string | null }) => {
  // Handle any additional cell change logic if needed
  console.log('Cell changed:', data)
}

// Computed
const navigationState = computed(() => {
  if (selectedGame.value) return 'lineup'
  if (selectedTeam.value) return 'games'
  return 'teams'
})

const playerCounts = computed(() => {
  if (!lineupGridRef.value || !lineupGridRef.value.getPlayerCount) {
    return {}
  }
  return lineupGridRef.value.getPlayerCount.value || {}
})

// Lifecycle hooks
onMounted(() => {
  // Set initial theme
  theme.global.name.value = 'light'
})
</script>

<template>
  <v-app>
    <v-app-bar app color="primary" dark>
      <v-toolbar-title>⚾ Baseball Lineup Manager</v-toolbar-title>
      <v-spacer></v-spacer>
      <v-btn icon>
        <v-icon>mdi-theme-light-dark</v-icon>
      </v-btn>
    </v-app-bar>

    <v-main class="overflow-hidden">
      <v-container fluid class="pa-0" style="height: 100%;">
        <v-row no-gutters style="height: 100%;">
          <!-- Sidebar -->
          <v-col cols="12" md="3" class="sidebar">
            <v-card flat tile height="100%" class="d-flex flex-column">
              <v-toolbar color="primary" dark flat>
                <v-btn
                  v-if="navigationState !== 'teams'"
                  icon
                  @click="navigationState === 'lineup' ? goBackToGames() : goBackToTeams()"
                  class="mr-2"
                >
                  <v-icon>mdi-arrow-left</v-icon>
                </v-btn>
                <v-toolbar-title>
                  {{
                    navigationState === 'lineup' ? 'Games' :
                    navigationState === 'games' ? selectedTeam?.name :
                    'Teams'
                  }}
                </v-toolbar-title>
                <v-spacer></v-spacer>
                <v-btn icon>
                  <v-icon>
                    {{
                      navigationState === 'lineup' ? 'mdi-account-plus' :
                      navigationState === 'games' ? 'mdi-calendar-plus' :
                      'mdi-plus'
                    }}
                  </v-icon>
                </v-btn>
              </v-toolbar>

              <!-- Teams List -->
              <v-list v-if="navigationState === 'teams'">
                <v-list-item
                  v-for="team in teams"
                  :key="team.id"
                  :title="team.name"
                  :subtitle="`${team.players.length} players • ${team.games.length} games • ${team.season}`"
                  @click="selectTeam(team)"
                  class="cursor-pointer"
                >
                  <template v-slot:prepend>
                    <v-avatar color="primary">
                      <v-icon>mdi-account-group</v-icon>
                    </v-avatar>
                  </template>
                  <template v-slot:append>
                    <v-icon>mdi-chevron-right</v-icon>
                  </template>
                </v-list-item>
              </v-list>

              <!-- Games List -->
              <v-list v-else-if="navigationState === 'games' && selectedTeam">
                <v-list-subheader>
                  {{ selectedTeam.headCoach }} • {{ selectedTeam.season }} • {{ selectedTeam.games.length }} games
                </v-list-subheader>
                <v-list-item
                  v-for="game in selectedTeam.games"
                  :key="game.id"
                  :title="`vs ${game.opponent}`"
                  :subtitle="`${new Date(game.date).toLocaleDateString()} • ${game.gameTime || 'TBD'} • ${game.homeAway}`"
                  @click="selectGame(game)"
                  class="cursor-pointer"
                >
                  <template v-slot:prepend>
                    <v-avatar :color="game.homeAway === 'Home' ? 'green' : 'orange'" size="small">
                      <v-icon size="small">{{ game.homeAway === 'Home' ? 'mdi-home' : 'mdi-airplane' }}</v-icon>
                    </v-avatar>
                  </template>
                  <template v-slot:append>
                    <v-icon>mdi-chevron-right</v-icon>
                  </template>
                </v-list-item>
              </v-list>

              <!-- Players List (when lineup view) -->
              <DraggableRoster
                v-if="navigationState === 'lineup' && selectedTeam && selectedGame"
                :players="selectedTeam.players"
                :selected-game="selectedGame"
                :selected-player-id="selectedPlayerId"
                :player-counts="playerCounts"
                @player-select="handlePlayerSelect"
                @drag-start="handleDragStart"
                @drag-end="handleDragEnd"
              />
            </v-card>
          </v-col>

          <!-- Main content -->
          <v-col cols="12" md="9" class="main-content">
            <v-card flat tile height="100%" class="d-flex flex-column">
              <v-toolbar color="grey-lighten-3" flat>
                <v-toolbar-title>Lineup</v-toolbar-title>
                <v-spacer></v-spacer>
                <v-btn color="primary" class="mr-2">
                  <v-icon start>mdi-content-save</v-icon>
                  Save
                </v-btn>
                <v-btn color="secondary">
                  <v-icon start>mdi-printer</v-icon>
                  Print
                </v-btn>
              </v-toolbar>
              
              <v-card-text class="flex-grow-1 overflow-y-auto">
                <!-- Teams View -->
                <div v-if="navigationState === 'teams'">
                  <h2 class="text-h5 mb-4">Select a team to manage</h2>
                  <v-alert type="info" variant="tonal">
                    <template v-slot:prepend>
                      <v-icon>mdi-information</v-icon>
                    </template>
                    <div>Choose a team from the sidebar to view games and create lineups.</div>
                  </v-alert>
                </div>

                <!-- Games View -->
                <div v-else-if="navigationState === 'games' && selectedTeam">
                  <h2 class="text-h5 mb-2">{{ selectedTeam.name }}</h2>
                  <p class="text-subtitle-1 text-medium-emphasis mb-4">
                    {{ selectedTeam.headCoach }} • {{ selectedTeam.season }}
                  </p>
                  <h3 class="text-h6 mb-3">Upcoming Games</h3>
                  <v-alert type="info" variant="tonal" class="mb-4">
                    <template v-slot:prepend>
                      <v-icon>mdi-information</v-icon>
                    </template>
                    <div>Select a game from the sidebar to create or edit its lineup.</div>
                  </v-alert>

                  <div class="d-flex flex-wrap gap-4">
                    <v-card
                      v-for="game in selectedTeam.games"
                      :key="game.id"
                      class="game-card"
                      elevation="2"
                      @click="selectGame(game)"
                      style="cursor: pointer; min-width: 300px;"
                    >
                      <v-card-title class="d-flex align-center">
                        <v-icon :color="game.homeAway === 'Home' ? 'green' : 'orange'" class="mr-2">
                          {{ game.homeAway === 'Home' ? 'mdi-home' : 'mdi-airplane' }}
                        </v-icon>
                        vs {{ game.opponent }}
                      </v-card-title>
                      <v-card-text>
                        <div><strong>Date:</strong> {{ new Date(game.date).toLocaleDateString() }}</div>
                        <div v-if="game.gameTime"><strong>Game Time:</strong> {{ game.gameTime }}</div>
                        <div v-if="game.arrivalTime"><strong>Arrival:</strong> {{ game.arrivalTime }}</div>
                        <div><strong>Location:</strong> {{ game.homeAway }}</div>
                        <div v-if="game.address" class="text-caption mt-1">{{ game.address }}</div>
                        <div v-if="game.notes" class="text-caption mt-2 font-italic">{{ game.notes }}</div>
                      </v-card-text>
                    </v-card>
                  </div>
                </div>

                <!-- Lineup View -->
                <div v-else-if="navigationState === 'lineup' && selectedGame && selectedTeam" class="lineup-view">
                  <div class="lineup-header">
                    <h2 class="text-h5 mb-2">{{ selectedTeam.name }} vs {{ selectedGame.opponent }}</h2>
                    <p class="text-subtitle-1 text-medium-emphasis mb-4">
                      {{ new Date(selectedGame.date).toLocaleDateString() }} • {{ selectedGame.gameTime || 'TBD' }} • {{ selectedGame.homeAway }}
                    </p>
                  </div>

                  <LineupGrid
                    ref="lineupGridRef"
                    :game="selectedGame"
                    :players="selectedTeam.players"
                    :show-keyboard-hints="true"
                    @player-select="handlePlayerSelect"
                    @cell-change="handleCellChange"
                    @drag-start="handleDragStart"
                    @drag-end="handleDragEnd"
                    class="flex-grow-1"
                  />
                </div>
              </v-card-text>
              
              <v-divider></v-divider>
              
              <v-card-actions class="pa-4">
                <v-spacer></v-spacer>
                <v-btn color="primary" variant="tonal">
                  <v-icon start>mdi-shuffle</v-icon>
                  Auto-Fill
                </v-btn>
                <v-btn color="error" variant="tonal">
                  <v-icon start>mdi-delete</v-icon>
                  Clear All
                </v-btn>
              </v-card-actions>
            </v-card>
          </v-col>
        </v-row>
      </v-container>
    </v-main>
    
    <v-footer app color="grey-lighten-4">
      <v-spacer></v-spacer>
      <div>Baseball Lineup Manager &copy; {{ new Date().getFullYear() }}</div>
    </v-footer>
  </v-app>
</template>

<style>
html, body {
  overflow: hidden !important;
}
</style>

<style scoped>
.sidebar {
  border-right: 1px solid rgba(0, 0, 0, 0.12);
  height: calc(100vh - 64px - 36px);
  overflow-y: auto;
}

.main-content {
  height: calc(100vh - 64px - 36px);
  overflow-y: auto;
}

.cursor-pointer {
  cursor: pointer;
}

.game-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  transition: all 0.2s ease-in-out;
}

.logo.vue:hover {
  filter: drop-shadow(0 0 2em #42b883aa);
}

.lineup-view {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.lineup-header {
  padding: 16px;
  border-bottom: 1px solid #e0e0e0;
  background: white;
}

.flex-grow-1 {
  flex-grow: 1;
  min-height: 0;
}
</style>
