# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

Lineup Lab is a local-first web application for baseball lineup management built with Vue 3 + TypeScript + Vuetify. It provides a visual grid-based interface where coaches can drag and drop players between innings and positions, with keyboard-friendly controls and rule-based highlighting.

**Key Features**:
- Grid-based lineup editor (innings × positions)
- Drag & drop player management with Ctrl+drag copy semantics
- Keyboard navigation with arrow keys, Enter for editing, Delete for clearing
- Rule-based visual highlighting for coaching insights
- Local-first storage with export/import capabilities
- Team and player photo management

## Development Commands

**Development server**: `npm run dev` (runs on port 3000)
**Build for production**: `npm run build`
**Preview production build**: `npm run preview`
**Type checking**: `vue-tsc -b` (included in build script)

## Architecture Overview

### Technology Stack
- **Frontend**: Vue 3 with Composition API and `<script setup>` syntax
- **TypeScript**: Strict mode enabled with comprehensive type checking
- **UI Framework**: Vuetify 3 (Material Design) with auto-import enabled
- **Build Tool**: Vite with Vue plugin and custom element support
- **State Management**: Pinia (configured but minimal usage in current POC)
- **Drag & Drop**: vuedraggable (SortableJS wrapper)
- **Icons**: Material Design Icons (@mdi/font)
- **Fonts**: Roboto font family

### Project Structure
```
src/
├── main.ts              # App entry point with Vuetify configuration
├── App.vue              # Root component with layout structure
├── components/          # Vue components
├── plugins/
│   └── vuetify.ts       # Vuetify configuration (secondary to main.ts)
└── styles/
    ├── main.scss        # Global styles and Vuetify imports
    ├── settings.scss    # Vuetify variable overrides
    └── variables.scss   # Custom SCSS variables
```

### Key Configuration Files
- `vite.config.ts`: Custom element support for `md-linedivider`, alias for `@/` src path, SCSS preprocessing
- `tsconfig.app.json`: Strict TypeScript with ESNext target, includes custom types directory
- `tsconfig.node.json`: Node-specific TypeScript config for build tools

### Current Implementation Status
This is a **Phase 0 POC** with hardcoded data. The current App.vue contains:
- Static team/player data for "Dallastown Cougars 12UB Robertson (Fall 2025)"
- Basic grid layout (innings × positions) using Vuetify tables
- Sidebar with team list placeholder
- Responsive layout with mobile considerations

## Data Model (Planned)

Based on `docs/TECHNICAL_SPEC.md`, the application will use these TypeScript interfaces:

```typescript
interface Player {
  id: string;
  firstName: string;
  lastName: string;
  throws?: 'L' | 'R' | 'S';
  bats?: 'L' | 'R' | 'S';
}

interface Team {
  id: string;
  name: string;
  season: string;
  headCoach?: string;
}

interface LineupCell {
  id: string;
  gameId: string;
  inning: number;
  positionKey: string; // 'P','C','1B','2B','SS','3B','LF','CF','RF','Bench1','Bench2'
  playerId?: string | null;
}
```

## Development Guidelines

### Component Development
- Use Vue 3 Composition API with `<script setup lang="ts">` syntax
- Leverage Vuetify's auto-import feature (components available without explicit imports)
- Follow Material Design principles via Vuetify components
- Implement responsive design using Vuetify's grid system

### Styling Approach
- Global styles in `src/styles/main.scss` with Vuetify integration
- Custom variables in `src/styles/variables.scss`
- Vuetify overrides in `src/styles/settings.scss`
- Use CSS custom properties for theme colors (defined in settings.scss)

### Key Features to Implement (Next Phases)
1. **Drag & Drop**: Integrate vuedraggable for roster → grid and grid → grid operations
2. **Keyboard Navigation**: Arrow keys, Enter for editing, Delete for clearing, Ctrl+C/V for copy/paste
3. **Rule Engine**: Configurable rules for highlighting (bench back-to-back, preferred positions, etc.)
4. **Local Persistence**: Dexie.js (IndexedDB) for local-first storage
5. **Export/Import**: CSV for Excel, JSON for backups

### Testing Data
The POC uses 15 hardcoded players: Adrian, Ben, Bennett, Caleb, Cam, Christian, Jack, Jaxon, Kaleb, Luca, Mason, Reid, Ryan, Seth, Tyler

## Vuetify Configuration

The app uses a centralized Vuetify setup in `main.ts` with:
- Material Design Icons as default icon set
- Light theme with custom primary color (#1976D2)
- Global component defaults (outlined text fields, flat buttons)
- Roboto font family integration

## Development Notes

- The app is designed for **local development** first (localhost:3000)
- **Local-first philosophy**: No backend required for POC, data export for backups
- **Future migration path**: Planned move to Tauri + SQLite for desktop reliability
- **Responsive design**: Mobile-first approach with Vuetify breakpoints
- **PWA ready**: Service worker registration included for production builds