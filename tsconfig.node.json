{"compilerOptions": {"composite": true, "skipLibCheck": true, "module": "ESNext", "moduleResolution": "bundler", "allowSyntheticDefaultImports": true, "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "noImplicitAny": true, "noImplicitThis": true, "noImplicitReturns": true, "strictNullChecks": true, "strictFunctionTypes": true, "strictPropertyInitialization": true, "noEmit": true, "esModuleInterop": true, "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "lib": ["ES2020", "DOM", "DOM.Iterable"], "target": "ES2020"}, "include": ["vite.config.ts"], "exclude": ["node_modules"]}