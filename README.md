# README.md

# Lineup Lab — desktop/web lineup editor (proof-of-concept)

**Short description**  
Lineup Lab is a small, local-first web app for building and managing baseball lineups at the inning × position level. It is designed to be simple to iterate on, privacy-focused (local-only by default), and keyboard-friendly — built for quickly assembling, editing, and exporting game lineups for youth baseball teams.

**Primary goals & capabilities (current scope)**

- **Three-level navigation system**: Teams → Games → Lineup creation with intuitive back navigation.
- **Team management**: Multiple teams with different player counts and game schedules.
- **Game management**: Comprehensive game details including opponent, date/time, arrival time, home/away, address, and notes.
- **Dynamic lineup grid**: Adjusts inning count based on selected game (12-16 innings) with canonical position columns.
- **Smart sidebar**: Context-aware left drawer showing teams list, games list, or player roster based on navigation state.
- Visual grid-based *game view* where each row is an inning and each column is a position (plus bench columns).
- **Future**: Drag to move players; **Ctrl+drag** to copy. Cells can contain the same player multiple times.
- **Future**: Keyboard-first behavior: arrow-key navigation, `Enter` to edit, `Delete` to clear, `Ctrl+X/C/V` cut/copy/paste, `Ctrl+Click` multi-select.
- **Future**: Click a player to **highlight that player everywhere** (grid and roster). Emphasized border for focused cell.
- **Future**: Rule-based visual highlights: toggleable rules with individually adjustable styles.
- **Future**: Persistent local storage for all teams/games/lineups (local-first architecture).
- **Future**: CSV export of lineups and JSON backups.
- **Future**: Player face photos stored per team with season snapshots.
- Multi-team proof-of-concept: *Wildcats 12U* (9 players), *Eagles 13U* (12 players), *Tigers 14U* (15 players) with realistic game schedules.

**Why this app exists (design intent)**

- Fast iteration: keep logic simple and allow embedding of UI logic where convenient so you can get a working POC quickly.  
- Local-first and private: the day-to-day workflow should not require cloud accounts. Export/import enables backups and later migration.  
- Keyboard and touch extendable: design for smooth keyboard operations now; tablet support can be added later.

**Data and preservation notes (user-facing)**

- By default this POC stores data locally in the browser (a robust browser store with export/import backups). You can export complete team and lineup data as JSON (for archival) and copy lineups as CSV for Excel.  
- Because local browser stores can be cleared by the browser, regular exports (JSON backups and CSVs) are recommended. The Technical Specification describes migration paths to file-backed SQLite (desktop/Tauri) for stronger guarantees.

**Future features noted (non-exhaustive)**

- Inning/field view: drag players onto a diamond for a specific inning.  
- Auto-lineup optimizer that uses player preferences and rules to propose lineups. (Important: optimizer must be flexible to support both generalist youth teams and specialized older teams.)  
- Multi-team and multi-season analytics: games played by position, percentages on-field for infield/outfield/bench, historical snapshots with photos preserved per-team.  
- Optional cloud sync / multi-device support if you later want to publish the app.

**Who this is for**  
Coaches and managers who want a small local tool to rapidly create and adjust lineups, experiment with inning-by-inning positioning, and export results to Excel for sharing or printing.

**Current POC**
- Environment: local web app (localhost dev).
- Teams:
  - **Wildcats 12U** (9 players): Alex Rodriguez, Ben Thompson, Carlos Martinez, Dylan Wilson, Ethan Davis, Felix Garcia, Gabriel Brown, Hunter Miller, Ian Anderson
  - **Eagles 13U** (12 players): Aaron Jackson, Blake White, Connor Taylor, Derek Thomas, Evan Harris, Finn Clark, Gavin Lewis, Henry Walker, Isaac Hall, Jake Allen, Kyle Young, Logan King
  - **Tigers 14U** (15 players): Adrian Wright, Brady Lopez, Caleb Hill, Daniel Scott, Eli Green, Frank Adams, George Baker, Henry Gonzalez, Ivan Nelson, Jack Carter, Kevin Mitchell, Liam Perez, Mason Roberts, Noah Turner, Owen Phillips
- Games: 2-4 games per team with realistic scheduling, opponents, and venue details.

---

